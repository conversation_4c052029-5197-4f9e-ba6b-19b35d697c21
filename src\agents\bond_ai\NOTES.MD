
### In _create_text_column
we have hardcoded  'p_type_id': 1,  # Type ID 1 for text columns. Do we need to make this with enums?
The related rpc agent_create_column is used on there


### In search_linkedin_profiles the number of rows 25 is hardcoded.
#### If table has fewer than 25 rows, create the needed rows  if row_count < 25:
#### here we should handle 402 status (insufficient credit with an alert to sysadmin)
#### reference for industry https://learn.microsoft.com/en-us/linkedin/shared/references/reference-tables/industry-codes-v2

